#!/bin/bash

# =============================================================================
# 自动化部署脚本 - MiniMax Agent Website
# =============================================================================
# 功能：构建网站并部署到远程服务器
# 作者：自动生成
# 日期：$(date +%Y-%m-%d)
# =============================================================================

# 设置脚本选项
set -e  # 遇到错误立即退出
set -u  # 使用未定义变量时退出
set -o pipefail  # 管道命令中任何一个失败都会导致整个管道失败

# =============================================================================
# 配置变量
# =============================================================================

# 服务器配置
SERVER_HOST="**************"
SERVER_USER="root"
SSH_KEY_PATH="/Users/<USER>/.ssh/compaysite.pem"
REMOTE_DIR="/user/app/bpmax_site_static_new"

# 本地配置
LOCAL_BUILD_DIR="./dist"
BACKUP_DIR="/tmp/bpmax_site_backup_$(date +%Y%m%d_%H%M%S)"

# 日志配置
LOG_FILE="deploy_$(date +%Y%m%d_%H%M%S).log"

# 颜色输出配置
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# =============================================================================
# 工具函数
# =============================================================================

# 日志函数
log() {
    local level=$1
    shift
    local message="$@"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")
            echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
        "WARN")
            echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
        "ERROR")
            echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
        "DEBUG")
            echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE"
            ;;
    esac
}

# 错误处理函数
error_exit() {
    log "ERROR" "$1"
    exit 1
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        error_exit "命令 '$1' 未找到，请先安装"
    fi
}

# 检查文件是否存在
check_file() {
    if [[ ! -f "$1" ]]; then
        error_exit "文件 '$1' 不存在"
    fi
}

# 检查目录是否存在
check_directory() {
    if [[ ! -d "$1" ]]; then
        error_exit "目录 '$1' 不存在"
    fi
}

# =============================================================================
# 预检查函数
# =============================================================================

pre_check() {
    log "INFO" "开始预检查..."
    
    # 检查必要的命令
    check_command "pnpm"
    check_command "ssh"
    check_command "rsync"
    check_command "scp"
    
    # 检查SSH密钥
    check_file "$SSH_KEY_PATH"
    
    # 检查SSH密钥权限
    local key_perms=$(stat -f "%A" "$SSH_KEY_PATH" 2>/dev/null || stat -c "%a" "$SSH_KEY_PATH" 2>/dev/null)
    if [[ "$key_perms" != "600" ]]; then
        log "WARN" "SSH密钥权限不正确，正在修复..."
        chmod 600 "$SSH_KEY_PATH"
    fi
    
    # 检查package.json
    check_file "package.json"
    
    log "INFO" "预检查完成"
}

# =============================================================================
# 服务器连接测试
# =============================================================================

test_server_connection() {
    log "INFO" "测试服务器连接..."
    
    if ssh -i "$SSH_KEY_PATH" -o ConnectTimeout=10 -o StrictHostKeyChecking=no \
           "$SERVER_USER@$SERVER_HOST" "echo 'Connection successful'" &>/dev/null; then
        log "INFO" "服务器连接测试成功"
    else
        error_exit "无法连接到服务器 $SERVER_HOST"
    fi
}

# =============================================================================
# 构建网站
# =============================================================================

build_website() {
    log "INFO" "开始构建网站..."
    
    # 清理之前的构建
    if [[ -d "$LOCAL_BUILD_DIR" ]]; then
        log "INFO" "清理之前的构建目录..."
        rm -rf "$LOCAL_BUILD_DIR"
    fi
    
    # 执行构建命令
    log "INFO" "执行构建命令: pnpm run build"
    if pnpm run build; then
        log "INFO" "网站构建成功"
    else
        error_exit "网站构建失败"
    fi
    
    # 验证构建结果
    check_directory "$LOCAL_BUILD_DIR"
    
    # 检查关键文件
    if [[ ! -f "$LOCAL_BUILD_DIR/index.html" ]]; then
        error_exit "构建结果中缺少 index.html 文件"
    fi
    
    log "INFO" "构建验证完成"
}

# =============================================================================
# 备份远程文件
# =============================================================================

backup_remote_files() {
    log "INFO" "开始备份远程服务器文件..."

    # 检查远程目录是否存在
    if ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_HOST" "test -d '$REMOTE_DIR'"; then
        log "INFO" "远程目录存在，开始备份..."

        # 在远程服务器创建备份
        local remote_backup_dir="/tmp/bpmax_site_backup_$(date +%Y%m%d_%H%M%S)"
        ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_HOST" \
            "mkdir -p '$remote_backup_dir' && cp -r '$REMOTE_DIR'/* '$remote_backup_dir'/ 2>/dev/null || true"

        log "INFO" "远程文件已备份到: $remote_backup_dir"
    else
        log "WARN" "远程目录不存在，跳过备份"

        # 创建远程目录
        ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_HOST" "mkdir -p '$REMOTE_DIR'"
        log "INFO" "已创建远程目录: $REMOTE_DIR"
    fi
}

# =============================================================================
# 部署文件到服务器
# =============================================================================

deploy_files() {
    log "INFO" "开始部署文件到服务器..."

    # 使用rsync同步文件
    log "INFO" "使用rsync同步文件..."

    # rsync选项说明:
    # -a: 归档模式，保持文件属性
    # -v: 详细输出
    # -z: 压缩传输
    # -h: 人类可读的输出
    # --progress: 显示进度
    # --delete: 删除目标目录中源目录没有的文件
    # --exclude: 排除特定文件

    if rsync -avzh --progress --delete \
             --exclude='*.log' \
             --exclude='.DS_Store' \
             --exclude='Thumbs.db' \
             -e "ssh -i '$SSH_KEY_PATH' -o StrictHostKeyChecking=no" \
             "$LOCAL_BUILD_DIR/" \
             "$SERVER_USER@$SERVER_HOST:$REMOTE_DIR/"; then
        log "INFO" "文件同步成功"
    else
        error_exit "文件同步失败"
    fi
}

# =============================================================================
# 设置文件权限
# =============================================================================

set_file_permissions() {
    log "INFO" "设置远程文件权限..."

    # 设置目录权限为755，文件权限为644
    ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_HOST" \
        "find '$REMOTE_DIR' -type d -exec chmod 755 {} \; && \
         find '$REMOTE_DIR' -type f -exec chmod 644 {} \;"

    if [[ $? -eq 0 ]]; then
        log "INFO" "文件权限设置成功"
    else
        error_exit "文件权限设置失败"
    fi
}

# =============================================================================
# 验证部署结果
# =============================================================================

verify_deployment() {
    log "INFO" "验证部署结果..."

    # 检查关键文件是否存在
    local key_files=("index.html" "assets")

    for file in "${key_files[@]}"; do
        if ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_HOST" "test -e '$REMOTE_DIR/$file'"; then
            log "INFO" "验证成功: $file 存在"
        else
            error_exit "验证失败: $file 不存在"
        fi
    done

    # 检查文件数量
    local local_file_count=$(find "$LOCAL_BUILD_DIR" -type f | wc -l | tr -d ' ')
    local remote_file_count=$(ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_HOST" \
                             "find '$REMOTE_DIR' -type f | wc -l" | tr -d ' ')

    log "INFO" "本地文件数量: $local_file_count"
    log "INFO" "远程文件数量: $remote_file_count"

    if [[ "$local_file_count" -eq "$remote_file_count" ]]; then
        log "INFO" "文件数量验证成功"
    else
        log "WARN" "文件数量不匹配，可能存在问题"
    fi

    # 检查index.html文件大小
    local local_index_size=$(stat -f%z "$LOCAL_BUILD_DIR/index.html" 2>/dev/null || stat -c%s "$LOCAL_BUILD_DIR/index.html" 2>/dev/null)
    local remote_index_size=$(ssh -i "$SSH_KEY_PATH" "$SERVER_USER@$SERVER_HOST" \
                             "stat -c%s '$REMOTE_DIR/index.html' 2>/dev/null || stat -f%z '$REMOTE_DIR/index.html' 2>/dev/null")

    if [[ "$local_index_size" -eq "$remote_index_size" ]]; then
        log "INFO" "index.html文件大小验证成功"
    else
        error_exit "index.html文件大小不匹配"
    fi

    log "INFO" "部署验证完成"
}

# =============================================================================
# 清理函数
# =============================================================================

cleanup() {
    log "INFO" "执行清理操作..."

    # 这里可以添加清理临时文件的逻辑
    # 例如清理本地临时文件等

    log "INFO" "清理完成"
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    log "INFO" "=========================================="
    log "INFO" "开始自动化部署流程"
    log "INFO" "=========================================="

    # 捕获退出信号，确保清理操作执行
    trap cleanup EXIT

    # 执行部署流程
    pre_check
    test_server_connection
    build_website
    backup_remote_files
    deploy_files
    set_file_permissions
    verify_deployment

    log "INFO" "=========================================="
    log "INFO" "部署完成！"
    log "INFO" "服务器地址: $SERVER_HOST"
    log "INFO" "部署目录: $REMOTE_DIR"
    log "INFO" "日志文件: $LOG_FILE"
    log "INFO" "=========================================="
}

# =============================================================================
# 脚本入口
# =============================================================================

# 检查是否以正确方式运行脚本
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
