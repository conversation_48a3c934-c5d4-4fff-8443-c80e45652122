import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

interface AnimatedSvgProps {
  className?: string;
  alt?: string;
}

const AnimatedSvg: React.FC<AnimatedSvgProps> = ({
  className = "w-full h-auto",
  alt = "BPMAX Hyperautomation Platform"
}) => {
  const { i18n } = useTranslation();
  const [isHovered, setIsHovered] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState(i18n.language || 'en');
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [tiltValues, setTiltValues] = useState({ rotateX: 0, rotateY: 0 });

  const containerRef = useRef<HTMLDivElement>(null);
  const animationFrameRef = useRef<number>();
  const lastMousePositionRef = useRef({ x: 0, y: 0 });
  const boundingRectRef = useRef<DOMRect | null>(null);
  const isAnimatingRef = useRef(false);

  // 监听语言变化
  useEffect(() => {
    setCurrentLanguage(i18n.language || 'en');
  }, [i18n.language]);

  // 根据语言选择SVG文件
  const getSvgPath = () => {
    // 确保使用当前语言状态，并且默认为英文
    const lang = currentLanguage || 'en';
    return lang.startsWith('zh')
      ? '/images/bpmaxdto.svg'
      : '/images/bomaxdtoen.svg';
  };

  // 计算倾斜角度
  const calculateTilt = () => {
    if (!containerRef.current || !isHovered) {
      return { rotateX: 0, rotateY: 0 };
    }

    const rect = containerRef.current.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    // 计算鼠标相对于中心的位置
    const deltaX = mousePosition.x - centerX;
    const deltaY = mousePosition.y - centerY;

    // 计算倾斜角度（限制在-15到15度之间）
    const maxTilt = 15;
    const rotateY = (deltaX / (rect.width / 2)) * maxTilt;
    const rotateX = -(deltaY / (rect.height / 2)) * maxTilt;

    return {
      rotateX: Math.max(-maxTilt, Math.min(maxTilt, rotateX)),
      rotateY: Math.max(-maxTilt, Math.min(maxTilt, rotateY))
    };
  };

  // 鼠标移动处理
  const handleMouseMove = (e: React.MouseEvent) => {
    setMousePosition({ x: e.clientX, y: e.clientY });
  };

  // 鼠标悬停处理
  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0, y: 0 });
  };

  const { rotateX, rotateY } = calculateTilt();

  return (
    <div
      ref={containerRef}
      className="relative cursor-pointer"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseMove={handleMouseMove}
      style={{
        perspective: '1000px',
        transformStyle: 'preserve-3d'
      }}
    >
      <img
        src={getSvgPath()}
        alt={alt}
        className={className}
        style={{
          filter: isHovered
            ? 'drop-shadow(0 12px 30px rgba(116, 77, 209, 0.25))'
            : 'drop-shadow(0 8px 20px rgba(116, 77, 209, 0.15))',
          transform: isHovered
            ? `scale(1.02) rotateX(${rotateX}deg) rotateY(${rotateY}deg)`
            : 'scale(1) rotateX(0deg) rotateY(0deg)',
          transition: 'transform 0.15s cubic-bezier(0.4, 0, 0.2, 1), filter 0.3s ease-out',
          transformOrigin: 'center center',
        }}
      />
    </div>
  );
};

export default AnimatedSvg;
