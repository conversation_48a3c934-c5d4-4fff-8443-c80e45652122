import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
}

const SEO = ({
  title = 'BPMAX - Transform Your Business with Hyperautomation',
  description = 'Transform your business operations with BPMAX hyperautomation platform. Streamline workflows, boost efficiency, and drive digital transformation with intelligent process automation.',
  keywords = 'hyperautomation, business automation, workflow automation, digital transformation, enterprise software, process optimization, BPMAX, intelligent automation',
  image = '/images/icons.png',
  url = 'https://bpmax.com',
  type = 'website'
}: SEOProps) => {
  const fullTitle = title.includes('BPMAX') ? title : `${title} | BPMAX`;
  
  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="author" content="BPMAX" />
      <meta name="robots" content="index, follow" />
      <link rel="canonical" href={url} />

      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={image} />
      <meta property="og:url" content={url} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content="BPMAX" />

      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />

      {/* Additional Meta Tags */}
      <meta name="theme-color" content="#bf01ff" />
      <meta name="msapplication-TileColor" content="#bf01ff" />
    </Helmet>
  );
};

export default SEO;
