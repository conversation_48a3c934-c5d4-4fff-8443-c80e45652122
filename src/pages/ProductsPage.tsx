import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Brain,
  Workflow,
  Database,
  Building2,
  Search,
  CheckCircle,
  ArrowRight,
  Zap,
  Shield,
  Users,
  BarChart3,
  Settings,
  Link as LinkIcon
} from 'lucide-react';
import { Link } from 'react-router-dom';
import SEO from '@/components/SEO';

const ProductsPage = () => {
  const [activeTab, setActiveTab] = useState('ai-engine');
  const { t } = useTranslation();
  const location = useLocation();

  // 处理URL锚点，自动切换到对应的产品标签
  useEffect(() => {
    const hash = location.hash.replace('#', '');
    if (hash && ['ai-engine', 'form-engine', 'data-engine', 'org-modeling', 'process-mining'].includes(hash)) {
      setActiveTab(hash);
    }
  }, [location.hash]);

  const products = [
    {
      id: 'ai-engine',
      icon: Brain,
      title: t('products.aiEngine.title') || 'AI智能流程引擎',
      subtitle: t('products.aiEngine.subtitle') || '智能化工作流自动化',
      description: t('products.aiEngine.description') || 'BPMAX的核心驱动力',
      image: '/images/ai-automation.png',
      features: Array.isArray(t('products.aiEngine.features', { returnObjects: true })) ? t('products.aiEngine.features', { returnObjects: true }) : [],
      benefits: Array.isArray(t('products.aiEngine.benefits', { returnObjects: true })) ? t('products.aiEngine.benefits', { returnObjects: true }) : [],
      color: 'brand'
    },
    {
      id: 'form-engine',
      icon: Workflow,
      title: t('products.formEngine.title') || '可视化表单引擎',
      subtitle: t('products.formEngine.subtitle') || '可视化表单设计器',
      description: t('products.formEngine.description') || '提供拖拽式可视化表单设计器',
      image: '/images/workflow-dashboard.jpg',
      features: Array.isArray(t('products.formEngine.features', { returnObjects: true })) ? t('products.formEngine.features', { returnObjects: true }) : [],
      benefits: Array.isArray(t('products.formEngine.benefits', { returnObjects: true })) ? t('products.formEngine.benefits', { returnObjects: true }) : [],
      color: 'green'
    },
    {
      id: 'data-engine',
      icon: Database,
      title: t('products.dataEngine.title') || '实时数据引擎',
      subtitle: t('products.dataEngine.subtitle') || '实时数据集成',
      description: t('products.dataEngine.description') || '负责流程数据的集成、处理和分析',
      image: '/images/data-analytics.jpg',
      features: Array.isArray(t('products.dataEngine.features', { returnObjects: true })) ? t('products.dataEngine.features', { returnObjects: true }) : [],
      benefits: Array.isArray(t('products.dataEngine.benefits', { returnObjects: true })) ? t('products.dataEngine.benefits', { returnObjects: true }) : [],
      color: 'purple'
    },
    {
      id: 'org-modeling',
      icon: Building2,
      title: t('products.orgModeling.title') || '组织架构建模',
      subtitle: t('products.orgModeling.subtitle') || '多维度组织管理',
      description: t('products.orgModeling.description') || '支持企业构建多维度、层次复杂的组织模型',
      image: '/images/team-collaboration.jpg',
      features: Array.isArray(t('products.orgModeling.features', { returnObjects: true })) ? t('products.orgModeling.features', { returnObjects: true }) : [],
      benefits: Array.isArray(t('products.orgModeling.benefits', { returnObjects: true })) ? t('products.orgModeling.benefits', { returnObjects: true }) : [],
      color: 'orange'
    },
    {
      id: 'process-mining',
      icon: Search,
      title: t('products.processMining.title') || '流程挖掘、追踪与仿真',
      subtitle: t('products.processMining.subtitle') || '智能流程发现与优化',
      description: t('products.processMining.description') || 'BPMAX区别于传统BPM软件的独特功能',
      image: '/images/business-intelligence.jpg',
      features: Array.isArray(t('products.processMining.features', { returnObjects: true })) ? t('products.processMining.features', { returnObjects: true }) : [],
      benefits: Array.isArray(t('products.processMining.benefits', { returnObjects: true })) ? t('products.processMining.benefits', { returnObjects: true }) : [],
      color: 'red'
    }
  ];

  const currentProduct = products.find(p => p.id === activeTab);

  const architectureFeatures = [
    {
      icon: Zap,
      title: t('products.architecture.features.performance.title'),
      description: t('products.architecture.features.performance.description')
    },
    {
      icon: Shield,
      title: t('products.architecture.features.security.title'),
      description: t('products.architecture.features.security.description')
    },
    {
      icon: LinkIcon,
      title: t('products.architecture.features.integration.title'),
      description: t('products.architecture.features.integration.description')
    },
    {
      icon: Users,
      title: t('products.architecture.features.multiTenancy.title'),
      description: t('products.architecture.features.multiTenancy.description')
    }
  ];

  return (
    <div className="space-y-0">
      <SEO
        title="Products - BPMAX Hyperautomation Platform"
        description="Explore BPMAX's comprehensive suite of hyperautomation products including AI Process Engine, Form Engine, Data Engine, and more. Transform your workflows with intelligent automation."
        keywords="hyperautomation products, AI process engine, form engine, data engine, workflow automation, intelligent automation software, BPMAX products"
        url="https://bpmax.com/products"
      />
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-brand-50 via-white to-purple-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-6">
            <Badge variant="secondary" className="bg-brand-100 text-brand-800 border-brand-200">
              {t('products.hero.badge')}
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900">
              {t('products.hero.title')}
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto">
              {t('products.hero.subtitle')}
            </p>
          </div>
        </div>
      </section>

      {/* Product Showcase */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            <TabsList className="grid w-full grid-cols-1 md:grid-cols-3 lg:grid-cols-5 h-auto p-1">
              {products.map((product) => {
                const IconComponent = product.icon;
                return (
                  <TabsTrigger
                    key={product.id}
                    value={product.id}
                    className="flex flex-col items-center p-4 space-y-2 data-[state=active]:bg-brand-50 data-[state=active]:text-brand-600"
                  >
                    <IconComponent className="w-6 h-6" />
                    <span className="text-sm font-medium text-center">{product.title}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {products.map((product) => {
              const IconComponent = product.icon;
              return (
                <TabsContent key={product.id} value={product.id} className="space-y-0">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <div className="space-y-8">
                      <div className="space-y-4">
                        <div className="flex items-center space-x-3">
                          <div className={`w-12 h-12 bg-${product.color}-100 rounded-lg flex items-center justify-center`}>
                            <IconComponent className={`w-6 h-6 text-${product.color}-600`} />
                          </div>
                          <div>
                            <h2 className="text-3xl font-bold text-gray-900">{product.title}</h2>
                            <p className="text-lg text-gray-600">{product.subtitle}</p>
                          </div>
                        </div>
                        <p className="text-gray-600 text-lg leading-relaxed">
                          {product.description}
                        </p>
                      </div>

                      <div className="space-y-6">
                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('products.common.keyFeatures')}</h3>
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                            {Array.isArray(product.features) ? product.features.map((feature, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                                <span className="text-sm text-gray-700">{feature}</span>
                              </div>
                            )) : null}
                          </div>
                        </div>

                        <div>
                          <h3 className="text-xl font-semibold text-gray-900 mb-4">{t('products.common.businessBenefits')}</h3>
                          <div className="space-y-2">
                            {Array.isArray(product.benefits) ? product.benefits.map((benefit, index) => (
                              <div key={index} className="flex items-start space-x-2">
                                <ArrowRight className="w-4 h-4 text-brand-500 mt-1 flex-shrink-0" />
                                <span className="text-gray-700">{benefit}</span>
                              </div>
                            )) : null}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="relative">
                      <img
                        src={product.image}
                        alt={product.title}
                        className="w-full h-auto rounded-2xl shadow-xl"
                      />

                    </div>
                  </div>
                </TabsContent>
              );
            })}
          </Tabs>
        </div>
      </section>

      {/* Architecture Features */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
              {t('products.architecture.title')}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {t('products.architecture.subtitle')}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {architectureFeatures.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card key={index} className="text-center p-6 hover:shadow-lg transition-all">
                  <CardHeader className="space-y-4">
                    <div className="w-12 h-12 bg-brand-100 rounded-lg flex items-center justify-center mx-auto">
                      <IconComponent className="w-6 h-6 text-brand-600" />
                    </div>
                    <CardTitle className="text-lg font-semibold">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Integration Ecosystem */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900">
                  {t('products.integration.title')}
                </h2>
                <p className="text-xl text-gray-600">
                  {t('products.integration.subtitle')}
                </p>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">{t('products.integration.categories.enterprise')}</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• {t('products.integration.systems.erp')}</li>
                      <li>• {t('products.integration.systems.crm')}</li>
                      <li>• {t('products.integration.systems.hrm')}</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">{t('products.integration.categories.communication')}</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• {t('products.integration.systems.email')}</li>
                      <li>• {t('products.integration.systems.messaging')}</li>
                      <li>• {t('products.integration.systems.videoConf')}</li>
                    </ul>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">{t('products.integration.categories.databases')}</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• {t('products.integration.systems.sqlNoSql')}</li>
                      <li>• {t('products.integration.systems.dataWarehouse')}</li>
                      <li>• {t('products.integration.systems.cloudStorage')}</li>
                    </ul>
                  </div>
                  <div className="space-y-2">
                    <h4 className="font-semibold text-gray-900">{t('products.integration.categories.analytics')}</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• {t('products.integration.systems.biTools')}</li>
                      <li>• {t('products.integration.systems.reporting')}</li>
                      <li>• {t('products.integration.systems.dashboard')}</li>
                    </ul>
                  </div>
                </div>
              </div>

              <Button className="bg-brand-600 hover:bg-brand-700" asChild>
                <Link to="/contact">
                  {t('products.integration.cta')}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
            </div>

            <div className="relative">
              <img
                src="/images/software-platform.png"
                alt="Integration Ecosystem"
                className="w-full h-auto rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-brand-600 to-purple-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="space-y-8">
            <h2 className="text-3xl md:text-4xl font-bold">
              {t('products.cta.title')}
            </h2>
            <p className="text-xl text-brand-100 max-w-3xl mx-auto">
              {t('products.cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to="/contact"
                className="bg-white text-brand-600 hover:bg-gray-100 hover:text-brand-700 px-8 py-3 h-10 inline-flex items-center justify-center gap-2 rounded-md font-medium transition-colors text-sm"
              >
                {t('products.cta.scheduleDemo')}
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                to="/solutions"
                className="border-2 border-white text-white hover:bg-white hover:text-brand-600 px-8 py-3 h-10 inline-flex items-center justify-center gap-2 rounded-md font-medium transition-colors text-sm"
              >
                {t('products.cta.viewSolutions')}
                <ArrowRight className="w-5 h-5" />
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ProductsPage;
