#!/bin/bash

# 批量替换蓝色为品牌色的脚本

echo "开始替换主题色..."

# 定义需要替换的文件列表
files=(
  "src/pages/SolutionsPage.tsx"
  "src/pages/CasesPage.tsx"
  "src/pages/AboutPage.tsx"
  "src/pages/ContactPage.tsx"
  "src/pages/NotFoundPage.tsx"
  "src/components/LanguageSwitcher.tsx"
)

# 定义替换规则
declare -A replacements=(
  ["bg-blue-50"]="bg-brand-50"
  ["bg-blue-100"]="bg-brand-100"
  ["bg-blue-200"]="bg-brand-200"
  ["bg-blue-600"]="bg-brand-600"
  ["bg-blue-700"]="bg-brand-700"
  ["text-blue-50"]="text-brand-50"
  ["text-blue-100"]="text-brand-100"
  ["text-blue-200"]="text-brand-200"
  ["text-blue-500"]="text-brand-500"
  ["text-blue-600"]="text-brand-600"
  ["text-blue-700"]="text-brand-700"
  ["text-blue-800"]="text-brand-800"
  ["border-blue-200"]="border-brand-200"
  ["border-blue-600"]="border-brand-600"
  ["hover:bg-blue-50"]="hover:bg-brand-50"
  ["hover:bg-blue-700"]="hover:bg-brand-700"
  ["hover:text-blue-600"]="hover:text-brand-600"
  ["from-blue-50"]="from-brand-50"
  ["from-blue-600"]="from-brand-600"
  ["to-blue-600"]="to-brand-600"
)

# 对每个文件执行替换
for file in "${files[@]}"; do
  if [ -f "$file" ]; then
    echo "处理文件: $file"
    
    # 对每个替换规则执行替换
    for old in "${!replacements[@]}"; do
      new="${replacements[$old]}"
      sed -i.bak "s/$old/$new/g" "$file"
    done
    
    # 删除备份文件
    rm -f "$file.bak"
    
    echo "完成: $file"
  else
    echo "文件不存在: $file"
  fi
done

echo "主题色替换完成！"
