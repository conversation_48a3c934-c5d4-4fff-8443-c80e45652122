<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>BPMAX - Transform Your Business with Hyperautomation</title>
  <meta name="description" content="Transform your business operations with BPMAX hyperautomation platform. Streamline workflows, boost efficiency, and drive digital transformation with intelligent process automation." />
  <meta name="keywords" content="hyperautomation, business automation, workflow automation, digital transformation, enterprise software, process optimization, BPMAX, intelligent automation" />
  <meta name="author" content="BPMAX" />
  <!-- Favicon -->
  <link rel="icon" type="image/png" href="/images/icons.png" />
  <link rel="shortcut icon" type="image/png" href="/images/icons.png" />
  <link rel="apple-touch-icon" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="57x57" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="60x60" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="72x72" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="76x76" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="114x114" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="120x120" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="144x144" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="152x152" href="/images/icons.png" />
  <link rel="apple-touch-icon" sizes="180x180" href="/images/icons.png" />
  <link rel="icon" type="image/png" sizes="16x16" href="/images/icons.png" />
  <link rel="icon" type="image/png" sizes="32x32" href="/images/icons.png" />
  <link rel="icon" type="image/png" sizes="96x96" href="/images/icons.png" />
  <link rel="icon" type="image/png" sizes="192x192" href="/images/icons.png" />
  <meta name="msapplication-TileImage" content="/images/icons.png" />
  <meta name="msapplication-TileColor" content="#bf01ff" />
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/src/main.tsx"></script>
</body>

</html>