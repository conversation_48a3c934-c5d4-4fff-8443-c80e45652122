# MiniMax Agent Website

基于 React + TypeScript + Vite + Tailwind CSS 构建的现代化网站项目。

## 技术栈

- **前端框架**: React 18
- **类型系统**: TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **UI组件**: Radix UI
- **包管理器**: pnpm
- **代码规范**: ESLint + Prettier
- **国际化**: i18next

## 本地开发

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd bpmax-new-site
   ```

2. **安装依赖**
   ```bash
   pnpm install
   ```

3. **启动开发服务器**
   ```bash
   pnpm dev
   ```

   开发服务器将在 `http://localhost:5173` 启动

4. **构建项目**
   ```bash
   pnpm build
   ```

5. **预览构建结果**
   ```bash
   pnpm preview
   ```

6. **代码检查**
   ```bash
   pnpm lint
   ```

### 开发说明

- 项目使用 Vite 作为开发服务器，支持热模块替换 (HMR)
- 所有源代码位于 `src/` 目录下
- 组件库使用 Radix UI，提供无障碍的基础组件
- 样式使用 Tailwind CSS，支持响应式设计和暗色模式
- 支持多语言国际化，配置文件在 `src/i18n/` 目录下

### 项目结构

```
src/
├── components/     # 可复用组件
├── pages/         # 页面组件
├── layout/        # 布局组件
├── hooks/         # 自定义 Hooks
├── lib/           # 工具函数和配置
├── i18n/          # 国际化配置
└── styles/        # 全局样式
```

## 部署

项目包含自动化部署脚本 `deploy.sh`，可以一键部署到远程服务器。

```bash
./deploy.sh
```

部署脚本会自动：
- 安装依赖
- 构建项目
- 上传到远程服务器
- 生成部署日志

## 开发工具配置

### VS Code 推荐扩展

项目已配置 VS Code 推荐扩展，首次打开项目时会提示安装：

- Tailwind CSS IntelliSense
- Prettier - Code formatter
- ESLint
- TypeScript and JavaScript Language Features
- Auto Rename Tag
- Path Intellisense

### 代码格式化

项目使用 Prettier 进行代码格式化，配置了保存时自动格式化。

### 代码检查

使用 ESLint 进行代码质量检查，配置了 TypeScript 和 React 相关规则。

## 常见问题

### 端口被占用

如果 5173 端口被占用，Vite 会自动选择下一个可用端口。

### 依赖安装失败

确保使用 pnpm 作为包管理器：
```bash
npm install -g pnpm
```

### 构建失败

检查 TypeScript 类型错误：
```bash
pnpm lint
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

---

## 高级配置

### 扩展 ESLint 配置

如果您正在开发生产应用程序，建议更新配置以启用类型感知的 lint 规则：

- 配置顶级 `parserOptions` 属性：

```js
export default tseslint.config({
  languageOptions: {
    // other options...
    parserOptions: {
      project: ['./tsconfig.node.json', './tsconfig.app.json'],
      tsconfigRootDir: import.meta.dirname,
    },
  },
})
```

- 将 `tseslint.configs.recommended` 替换为 `tseslint.configs.recommendedTypeChecked` 或 `tseslint.configs.strictTypeChecked`
- 可选择添加 `...tseslint.configs.stylisticTypeChecked`
- 安装 [eslint-plugin-react](https://github.com/jsx-eslint/eslint-plugin-react) 并更新配置：

```js
// eslint.config.js
import react from 'eslint-plugin-react'

export default tseslint.config({
  // Set the react version
  settings: { react: { version: '18.3' } },
  plugins: {
    // Add the react plugin
    react,
  },
  rules: {
    // other rules...
    // Enable its recommended rules
    ...react.configs.recommended.rules,
    ...react.configs['jsx-runtime'].rules,
  },
})
```

### Vite 插件

当前使用的官方插件：

- [@vitejs/plugin-react](https://github.com/vitejs/vite-plugin-react/blob/main/packages/plugin-react/README.md) 使用 [Babel](https://babeljs.io/) 进行 Fast Refresh
- [@vitejs/plugin-react-swc](https://github.com/vitejs/vite-plugin-react-swc) 使用 [SWC](https://swc.rs/) 进行 Fast Refresh
